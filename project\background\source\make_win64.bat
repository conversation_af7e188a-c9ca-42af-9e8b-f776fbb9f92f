﻿@echo off
chcp 65001 >nul 2>&1
setlocal enabledelayedexpansion

REM Check build type parameter
set BUILD_TYPE=Debug
if "%1"=="Release" set BUILD_TYPE=Release
if "%1"=="release" set BUILD_TYPE=Release

echo Build type: %BUILD_TYPE%

if not exist "build_win64" (
	mkdir build_win64
)
pushd build_win64
cmake ../ ^
		-G "Visual Studio 17 2022" -A x64 ^
		-DCMAKE_BUILD_TYPE=%BUILD_TYPE%^
		-DMAKE_INSTALL=ON ^
		-DCMAKE_INSTALL_PREFIX=../../cute_root ^
		..

cmake --build ./ --config %BUILD_TYPE%
cmake --install ./ --config %BUILD_TYPE%
popd

echo "install done (%BUILD_TYPE%)"

