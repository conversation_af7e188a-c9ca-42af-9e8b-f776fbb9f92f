﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="MinSizeRel|x64">
      <Configuration>MinSizeRel</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="RelWithDebInfo|x64">
      <Configuration>RelWithDebInfo</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{28C7A959-F7CB-31EB-87C3-EB074755F77A}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0.26100.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>pcute_usrzone</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\build_win64\Debug\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">pcute_usrzone.dir\Debug\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">pcute_usrzone</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\build_win64\Release\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">pcute_usrzone.dir\Release\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Release|x64'">pcute_usrzone</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Release|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\build_win64\MinSizeRel\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">pcute_usrzone.dir\MinSizeRel\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">pcute_usrzone</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\build_win64\RelWithDebInfo\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">pcute_usrzone.dir\RelWithDebInfo\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">pcute_usrzone</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</GenerateManifest>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src\dvo;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src\protocal;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src\serva;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src\servd;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src\servs;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src\sql;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src\tools;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src\vo;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src\wrapper;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src\wrapper\dvo;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\zlib_inc-NOTFOUND;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\uv_inc-NOTFOUND;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\..\third_lib\zlog\include;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\tcmalloc_inc-NOTFOUND;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\mysql_inc-NOTFOUND;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\..\third_lib\mbedtls\include;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\..\third_lib\libcopp\include;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\sqlpp_inc-NOTFOUND;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\cute_inc-NOTFOUND;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\nav_inc-NOTFOUND;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\m_sdvo_inc-NOTFOUND;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\m_spvo_inc-NOTFOUND;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <DisableSpecificWarnings>4100;4127;4201;4206;4210;4232;4456;4457;4459;4706;4996;4819</DisableSpecificWarnings>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>Disabled</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>Disabled</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level3</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;MODULE_NAME="usrzone";CMAKE_INTDIR="Debug"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_DEBUG;_WINDOWS;MODULE_NAME=\"usrzone\";CMAKE_INTDIR=\"Debug\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src\dvo;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src\protocal;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src\serva;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src\servd;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src\servs;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src\sql;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src\tools;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src\vo;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src\wrapper;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src\wrapper\dvo;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\zlib_inc-NOTFOUND;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\uv_inc-NOTFOUND;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\..\third_lib\zlog\include;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\tcmalloc_inc-NOTFOUND;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\mysql_inc-NOTFOUND;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\..\third_lib\mbedtls\include;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\..\third_lib\libcopp\include;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\sqlpp_inc-NOTFOUND;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\cute_inc-NOTFOUND;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\nav_inc-NOTFOUND;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\m_sdvo_inc-NOTFOUND;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\m_spvo_inc-NOTFOUND;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src\dvo;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src\protocal;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src\serva;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src\servd;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src\servs;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src\sql;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src\tools;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src\vo;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src\wrapper;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src\wrapper\dvo;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\zlib_inc-NOTFOUND;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\uv_inc-NOTFOUND;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\..\third_lib\zlog\include;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\tcmalloc_inc-NOTFOUND;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\mysql_inc-NOTFOUND;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\..\third_lib\mbedtls\include;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\..\third_lib\libcopp\include;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\sqlpp_inc-NOTFOUND;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\cute_inc-NOTFOUND;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\nav_inc-NOTFOUND;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\m_sdvo_inc-NOTFOUND;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\m_spvo_inc-NOTFOUND;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Link>
      <AdditionalDependencies>-Wl,--start-group;zlib_lib-NOTFOUND.lib;uv_lib-NOTFOUND.lib;zlog_lib-NOTFOUND.lib;tcmalloc_lib-NOTFOUND.lib;mysql_lib-NOTFOUND.lib;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\..\third_lib\mbedtls\lib\mbedtls.lib;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\..\third_lib\mbedtls\lib\mbedcrypto.lib;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\..\third_lib\mbedtls\lib\mbedx509.lib;libcopp_lib-NOTFOUND.lib;libcopp_task-NOTFOUND.lib;sqlpp_lib-NOTFOUND.lib;cute_lib-NOTFOUND.lib;nav_lib-NOTFOUND.lib;m_sdvo_lib-NOTFOUND.lib;m_spvo_lib-NOTFOUND.lib;Debug\pcute_aux.lib;-Wl,--end-group;zlib_lib-NOTFOUND.lib;uv_lib-NOTFOUND.lib;zlog_lib-NOTFOUND.lib;tcmalloc_lib-NOTFOUND.lib;mysql_lib-NOTFOUND.lib;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\..\third_lib\mbedtls\lib\mbedtls.lib;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\..\third_lib\mbedtls\lib\mbedcrypto.lib;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\..\third_lib\mbedtls\lib\mbedx509.lib;libcopp_lib-NOTFOUND.lib;libcopp_task-NOTFOUND.lib;sqlpp_lib-NOTFOUND.lib;cute_lib-NOTFOUND.lib;nav_lib-NOTFOUND.lib;m_sdvo_lib-NOTFOUND.lib;m_spvo_lib-NOTFOUND.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>D:/BaiduNetdiskDownload/mhgd/rc/project/usrzone/source/build_win64/Debug/pcute_usrzone.lib</ImportLibrary>
      <ProgramDataBaseFile>D:/BaiduNetdiskDownload/mhgd/rc/project/usrzone/source/build_win64/Debug/pcute_usrzone.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src\dvo;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src\protocal;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src\serva;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src\servd;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src\servs;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src\sql;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src\tools;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src\vo;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src\wrapper;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src\wrapper\dvo;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\zlib_inc-NOTFOUND;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\uv_inc-NOTFOUND;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\..\third_lib\zlog\include;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\tcmalloc_inc-NOTFOUND;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\mysql_inc-NOTFOUND;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\..\third_lib\mbedtls\include;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\..\third_lib\libcopp\include;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\sqlpp_inc-NOTFOUND;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\cute_inc-NOTFOUND;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\nav_inc-NOTFOUND;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\m_sdvo_inc-NOTFOUND;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\m_spvo_inc-NOTFOUND;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <DisableSpecificWarnings>4100;4127;4201;4206;4210;4232;4456;4457;4459;4706;4996;4819</DisableSpecificWarnings>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level3</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;MODULE_NAME="usrzone";CMAKE_INTDIR="Release"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;MODULE_NAME=\"usrzone\";CMAKE_INTDIR=\"Release\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src\dvo;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src\protocal;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src\serva;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src\servd;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src\servs;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src\sql;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src\tools;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src\vo;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src\wrapper;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src\wrapper\dvo;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\zlib_inc-NOTFOUND;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\uv_inc-NOTFOUND;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\..\third_lib\zlog\include;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\tcmalloc_inc-NOTFOUND;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\mysql_inc-NOTFOUND;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\..\third_lib\mbedtls\include;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\..\third_lib\libcopp\include;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\sqlpp_inc-NOTFOUND;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\cute_inc-NOTFOUND;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\nav_inc-NOTFOUND;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\m_sdvo_inc-NOTFOUND;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\m_spvo_inc-NOTFOUND;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src\dvo;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src\protocal;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src\serva;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src\servd;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src\servs;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src\sql;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src\tools;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src\vo;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src\wrapper;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src\wrapper\dvo;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\zlib_inc-NOTFOUND;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\uv_inc-NOTFOUND;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\..\third_lib\zlog\include;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\tcmalloc_inc-NOTFOUND;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\mysql_inc-NOTFOUND;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\..\third_lib\mbedtls\include;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\..\third_lib\libcopp\include;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\sqlpp_inc-NOTFOUND;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\cute_inc-NOTFOUND;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\nav_inc-NOTFOUND;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\m_sdvo_inc-NOTFOUND;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\m_spvo_inc-NOTFOUND;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Link>
      <AdditionalDependencies>-Wl,--start-group;zlib_lib-NOTFOUND.lib;uv_lib-NOTFOUND.lib;zlog_lib-NOTFOUND.lib;tcmalloc_lib-NOTFOUND.lib;mysql_lib-NOTFOUND.lib;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\..\third_lib\mbedtls\lib\mbedtls.lib;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\..\third_lib\mbedtls\lib\mbedcrypto.lib;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\..\third_lib\mbedtls\lib\mbedx509.lib;libcopp_lib-NOTFOUND.lib;libcopp_task-NOTFOUND.lib;sqlpp_lib-NOTFOUND.lib;cute_lib-NOTFOUND.lib;nav_lib-NOTFOUND.lib;m_sdvo_lib-NOTFOUND.lib;m_spvo_lib-NOTFOUND.lib;Release\pcute_aux.lib;-Wl,--end-group;zlib_lib-NOTFOUND.lib;uv_lib-NOTFOUND.lib;zlog_lib-NOTFOUND.lib;tcmalloc_lib-NOTFOUND.lib;mysql_lib-NOTFOUND.lib;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\..\third_lib\mbedtls\lib\mbedtls.lib;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\..\third_lib\mbedtls\lib\mbedcrypto.lib;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\..\third_lib\mbedtls\lib\mbedx509.lib;libcopp_lib-NOTFOUND.lib;libcopp_task-NOTFOUND.lib;sqlpp_lib-NOTFOUND.lib;cute_lib-NOTFOUND.lib;nav_lib-NOTFOUND.lib;m_sdvo_lib-NOTFOUND.lib;m_spvo_lib-NOTFOUND.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>D:/BaiduNetdiskDownload/mhgd/rc/project/usrzone/source/build_win64/Release/pcute_usrzone.lib</ImportLibrary>
      <ProgramDataBaseFile>D:/BaiduNetdiskDownload/mhgd/rc/project/usrzone/source/build_win64/Release/pcute_usrzone.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src\dvo;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src\protocal;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src\serva;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src\servd;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src\servs;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src\sql;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src\tools;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src\vo;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src\wrapper;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src\wrapper\dvo;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\zlib_inc-NOTFOUND;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\uv_inc-NOTFOUND;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\..\third_lib\zlog\include;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\tcmalloc_inc-NOTFOUND;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\mysql_inc-NOTFOUND;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\..\third_lib\mbedtls\include;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\..\third_lib\libcopp\include;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\sqlpp_inc-NOTFOUND;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\cute_inc-NOTFOUND;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\nav_inc-NOTFOUND;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\m_sdvo_inc-NOTFOUND;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\m_spvo_inc-NOTFOUND;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <DisableSpecificWarnings>4100;4127;4201;4206;4210;4232;4456;4457;4459;4706;4996;4819</DisableSpecificWarnings>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>MinSpace</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level3</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;MODULE_NAME="usrzone";CMAKE_INTDIR="MinSizeRel"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;MODULE_NAME=\"usrzone\";CMAKE_INTDIR=\"MinSizeRel\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src\dvo;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src\protocal;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src\serva;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src\servd;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src\servs;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src\sql;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src\tools;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src\vo;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src\wrapper;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src\wrapper\dvo;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\zlib_inc-NOTFOUND;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\uv_inc-NOTFOUND;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\..\third_lib\zlog\include;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\tcmalloc_inc-NOTFOUND;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\mysql_inc-NOTFOUND;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\..\third_lib\mbedtls\include;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\..\third_lib\libcopp\include;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\sqlpp_inc-NOTFOUND;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\cute_inc-NOTFOUND;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\nav_inc-NOTFOUND;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\m_sdvo_inc-NOTFOUND;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\m_spvo_inc-NOTFOUND;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src\dvo;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src\protocal;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src\serva;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src\servd;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src\servs;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src\sql;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src\tools;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src\vo;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src\wrapper;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src\wrapper\dvo;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\zlib_inc-NOTFOUND;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\uv_inc-NOTFOUND;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\..\third_lib\zlog\include;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\tcmalloc_inc-NOTFOUND;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\mysql_inc-NOTFOUND;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\..\third_lib\mbedtls\include;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\..\third_lib\libcopp\include;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\sqlpp_inc-NOTFOUND;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\cute_inc-NOTFOUND;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\nav_inc-NOTFOUND;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\m_sdvo_inc-NOTFOUND;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\m_spvo_inc-NOTFOUND;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Link>
      <AdditionalDependencies>-Wl,--start-group;zlib_lib-NOTFOUND.lib;uv_lib-NOTFOUND.lib;zlog_lib-NOTFOUND.lib;tcmalloc_lib-NOTFOUND.lib;mysql_lib-NOTFOUND.lib;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\..\third_lib\mbedtls\lib\mbedtls.lib;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\..\third_lib\mbedtls\lib\mbedcrypto.lib;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\..\third_lib\mbedtls\lib\mbedx509.lib;libcopp_lib-NOTFOUND.lib;libcopp_task-NOTFOUND.lib;sqlpp_lib-NOTFOUND.lib;cute_lib-NOTFOUND.lib;nav_lib-NOTFOUND.lib;m_sdvo_lib-NOTFOUND.lib;m_spvo_lib-NOTFOUND.lib;MinSizeRel\pcute_aux.lib;-Wl,--end-group;zlib_lib-NOTFOUND.lib;uv_lib-NOTFOUND.lib;zlog_lib-NOTFOUND.lib;tcmalloc_lib-NOTFOUND.lib;mysql_lib-NOTFOUND.lib;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\..\third_lib\mbedtls\lib\mbedtls.lib;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\..\third_lib\mbedtls\lib\mbedcrypto.lib;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\..\third_lib\mbedtls\lib\mbedx509.lib;libcopp_lib-NOTFOUND.lib;libcopp_task-NOTFOUND.lib;sqlpp_lib-NOTFOUND.lib;cute_lib-NOTFOUND.lib;nav_lib-NOTFOUND.lib;m_sdvo_lib-NOTFOUND.lib;m_spvo_lib-NOTFOUND.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>D:/BaiduNetdiskDownload/mhgd/rc/project/usrzone/source/build_win64/MinSizeRel/pcute_usrzone.lib</ImportLibrary>
      <ProgramDataBaseFile>D:/BaiduNetdiskDownload/mhgd/rc/project/usrzone/source/build_win64/MinSizeRel/pcute_usrzone.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src\dvo;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src\protocal;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src\serva;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src\servd;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src\servs;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src\sql;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src\tools;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src\vo;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src\wrapper;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src\wrapper\dvo;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\zlib_inc-NOTFOUND;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\uv_inc-NOTFOUND;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\..\third_lib\zlog\include;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\tcmalloc_inc-NOTFOUND;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\mysql_inc-NOTFOUND;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\..\third_lib\mbedtls\include;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\..\third_lib\libcopp\include;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\sqlpp_inc-NOTFOUND;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\cute_inc-NOTFOUND;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\nav_inc-NOTFOUND;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\m_sdvo_inc-NOTFOUND;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\m_spvo_inc-NOTFOUND;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <DisableSpecificWarnings>4100;4127;4201;4206;4210;4232;4456;4457;4459;4706;4996;4819</DisableSpecificWarnings>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level3</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;MODULE_NAME="usrzone";CMAKE_INTDIR="RelWithDebInfo"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;MODULE_NAME=\"usrzone\";CMAKE_INTDIR=\"RelWithDebInfo\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src\dvo;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src\protocal;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src\serva;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src\servd;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src\servs;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src\sql;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src\tools;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src\vo;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src\wrapper;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src\wrapper\dvo;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\zlib_inc-NOTFOUND;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\uv_inc-NOTFOUND;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\..\third_lib\zlog\include;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\tcmalloc_inc-NOTFOUND;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\mysql_inc-NOTFOUND;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\..\third_lib\mbedtls\include;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\..\third_lib\libcopp\include;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\sqlpp_inc-NOTFOUND;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\cute_inc-NOTFOUND;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\nav_inc-NOTFOUND;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\m_sdvo_inc-NOTFOUND;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\m_spvo_inc-NOTFOUND;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src\dvo;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src\protocal;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src\serva;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src\servd;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src\servs;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src\sql;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src\tools;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src\vo;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src\wrapper;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src\wrapper\dvo;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\zlib_inc-NOTFOUND;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\uv_inc-NOTFOUND;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\..\third_lib\zlog\include;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\tcmalloc_inc-NOTFOUND;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\mysql_inc-NOTFOUND;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\..\third_lib\mbedtls\include;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\..\third_lib\libcopp\include;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\sqlpp_inc-NOTFOUND;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\cute_inc-NOTFOUND;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\nav_inc-NOTFOUND;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\m_sdvo_inc-NOTFOUND;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\m_spvo_inc-NOTFOUND;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Link>
      <AdditionalDependencies>-Wl,--start-group;zlib_lib-NOTFOUND.lib;uv_lib-NOTFOUND.lib;zlog_lib-NOTFOUND.lib;tcmalloc_lib-NOTFOUND.lib;mysql_lib-NOTFOUND.lib;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\..\third_lib\mbedtls\lib\mbedtls.lib;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\..\third_lib\mbedtls\lib\mbedcrypto.lib;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\..\third_lib\mbedtls\lib\mbedx509.lib;libcopp_lib-NOTFOUND.lib;libcopp_task-NOTFOUND.lib;sqlpp_lib-NOTFOUND.lib;cute_lib-NOTFOUND.lib;nav_lib-NOTFOUND.lib;m_sdvo_lib-NOTFOUND.lib;m_spvo_lib-NOTFOUND.lib;RelWithDebInfo\pcute_aux.lib;-Wl,--end-group;zlib_lib-NOTFOUND.lib;uv_lib-NOTFOUND.lib;zlog_lib-NOTFOUND.lib;tcmalloc_lib-NOTFOUND.lib;mysql_lib-NOTFOUND.lib;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\..\third_lib\mbedtls\lib\mbedtls.lib;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\..\third_lib\mbedtls\lib\mbedcrypto.lib;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\..\third_lib\mbedtls\lib\mbedx509.lib;libcopp_lib-NOTFOUND.lib;libcopp_task-NOTFOUND.lib;sqlpp_lib-NOTFOUND.lib;cute_lib-NOTFOUND.lib;nav_lib-NOTFOUND.lib;m_sdvo_lib-NOTFOUND.lib;m_spvo_lib-NOTFOUND.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>D:/BaiduNetdiskDownload/mhgd/rc/project/usrzone/source/build_win64/RelWithDebInfo/pcute_usrzone.lib</ImportLibrary>
      <ProgramDataBaseFile>D:/BaiduNetdiskDownload/mhgd/rc/project/usrzone/source/build_win64/RelWithDebInfo/pcute_usrzone.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\CMakeLists.txt">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Building Custom Rule D:/BaiduNetdiskDownload/mhgd/rc/project/usrzone/source/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SD:/BaiduNetdiskDownload/mhgd/rc/project/usrzone/source -BD:/BaiduNetdiskDownload/mhgd/rc/project/usrzone/source/build_win64 --check-stamp-file D:/BaiduNetdiskDownload/mhgd/rc/project/usrzone/source/build_win64/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeCCompiler.cmake.in;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeCCompilerABI.c;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeCInformation.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeCXXCompiler.cmake.in;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeCXXCompilerABI.cpp;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeCXXInformation.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeCheckCompilerFlagCommonPatterns.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeCommonLanguageInclude.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeCompilerIdDetection.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeDependentOption.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeDetermineCCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeDetermineCXXCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeDetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeDetermineCompilerABI.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeDetermineCompilerId.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeDetermineCompilerSupport.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeDetermineRCCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeDetermineSystem.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeFindBinUtils.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeGenericSystem.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeInitializeConfigs.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeLanguageInformation.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakePackageConfigHelpers.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeParseImplicitIncludeInfo.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeParseImplicitLinkInfo.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeParseLibraryArchitecture.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeRCCompiler.cmake.in;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeRCInformation.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeSystem.cmake.in;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeSystemSpecificInformation.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeSystemSpecificInitialize.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeTestCCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeTestCXXCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeTestCompilerCommon.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeTestRCCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CTest.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CTestUseLaunchers.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CheckCCompilerFlag.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CheckCSourceCompiles.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\ADSP-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\ARMCC-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\ARMClang-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\AppleClang-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\Borland-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\Bruce-C-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\CMakeCommonCompilerMacros.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\Clang-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\Clang-DetermineCompilerInternal.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\Compaq-C-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\Compaq-CXX-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\Cray-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\CrayClang-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\Embarcadero-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\Fujitsu-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\FujitsuClang-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\GHS-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\GNU-C-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\GNU-CXX-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\HP-C-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\HP-CXX-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\IAR-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\IBMCPP-C-DetermineVersionInternal.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\IBMCPP-CXX-DetermineVersionInternal.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\IBMClang-C-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\IBMClang-CXX-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\Intel-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\IntelLLVM-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\LCC-C-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\LCC-CXX-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\MSVC-C.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\MSVC-CXX.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\MSVC-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\MSVC.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\NVHPC-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\NVIDIA-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\OpenWatcom-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\OrangeC-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\PGI-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\PathScale-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\SCO-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\SDCC-C-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\SunPro-C-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\SunPro-CXX-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\TI-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\TIClang-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\Tasking-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\TinyCC-C-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\VisualAge-C-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\VisualAge-CXX-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\Watcom-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\XL-C-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\XL-CXX-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\XLClang-C-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\XLClang-CXX-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\zOS-C-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\zOS-CXX-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CompilerId\VS-10.vcxproj.in;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\GNUInstallDirs.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Internal\CMakeCLinkerInformation.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Internal\CMakeCXXLinkerInformation.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Internal\CMakeCommonLinkerInformation.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Internal\CMakeDetermineLinkerId.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Internal\CheckCompilerFlag.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Internal\CheckFlagCommonConfig.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Internal\CheckSourceCompiles.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Internal\FeatureTesting.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Linker\Windows-MSVC-C.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Linker\Windows-MSVC-CXX.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Linker\Windows-MSVC.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Windows-Determine-CXX.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Windows-Initialize.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Windows-MSVC-C.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Windows-MSVC-CXX.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Windows-MSVC.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Windows.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\WindowsPaths.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\WriteBasicConfigVersionFile.cmake;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\build_win64\CMakeFiles\3.31.6-msvc6\CMakeCCompiler.cmake;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\build_win64\CMakeFiles\3.31.6-msvc6\CMakeCXXCompiler.cmake;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\build_win64\CMakeFiles\3.31.6-msvc6\CMakeRCCompiler.cmake;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\build_win64\CMakeFiles\3.31.6-msvc6\CMakeSystem.cmake;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\tools\cmake_tools.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\build_win64\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Building Custom Rule D:/BaiduNetdiskDownload/mhgd/rc/project/usrzone/source/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SD:/BaiduNetdiskDownload/mhgd/rc/project/usrzone/source -BD:/BaiduNetdiskDownload/mhgd/rc/project/usrzone/source/build_win64 --check-stamp-file D:/BaiduNetdiskDownload/mhgd/rc/project/usrzone/source/build_win64/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeCCompiler.cmake.in;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeCCompilerABI.c;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeCInformation.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeCXXCompiler.cmake.in;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeCXXCompilerABI.cpp;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeCXXInformation.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeCheckCompilerFlagCommonPatterns.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeCommonLanguageInclude.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeCompilerIdDetection.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeDependentOption.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeDetermineCCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeDetermineCXXCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeDetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeDetermineCompilerABI.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeDetermineCompilerId.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeDetermineCompilerSupport.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeDetermineRCCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeDetermineSystem.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeFindBinUtils.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeGenericSystem.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeInitializeConfigs.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeLanguageInformation.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakePackageConfigHelpers.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeParseImplicitIncludeInfo.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeParseImplicitLinkInfo.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeParseLibraryArchitecture.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeRCCompiler.cmake.in;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeRCInformation.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeSystem.cmake.in;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeSystemSpecificInformation.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeSystemSpecificInitialize.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeTestCCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeTestCXXCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeTestCompilerCommon.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeTestRCCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CTest.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CTestUseLaunchers.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CheckCCompilerFlag.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CheckCSourceCompiles.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\ADSP-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\ARMCC-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\ARMClang-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\AppleClang-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\Borland-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\Bruce-C-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\CMakeCommonCompilerMacros.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\Clang-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\Clang-DetermineCompilerInternal.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\Compaq-C-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\Compaq-CXX-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\Cray-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\CrayClang-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\Embarcadero-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\Fujitsu-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\FujitsuClang-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\GHS-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\GNU-C-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\GNU-CXX-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\HP-C-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\HP-CXX-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\IAR-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\IBMCPP-C-DetermineVersionInternal.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\IBMCPP-CXX-DetermineVersionInternal.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\IBMClang-C-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\IBMClang-CXX-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\Intel-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\IntelLLVM-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\LCC-C-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\LCC-CXX-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\MSVC-C.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\MSVC-CXX.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\MSVC-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\MSVC.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\NVHPC-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\NVIDIA-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\OpenWatcom-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\OrangeC-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\PGI-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\PathScale-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\SCO-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\SDCC-C-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\SunPro-C-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\SunPro-CXX-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\TI-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\TIClang-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\Tasking-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\TinyCC-C-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\VisualAge-C-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\VisualAge-CXX-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\Watcom-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\XL-C-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\XL-CXX-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\XLClang-C-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\XLClang-CXX-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\zOS-C-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\zOS-CXX-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CompilerId\VS-10.vcxproj.in;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\GNUInstallDirs.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Internal\CMakeCLinkerInformation.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Internal\CMakeCXXLinkerInformation.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Internal\CMakeCommonLinkerInformation.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Internal\CMakeDetermineLinkerId.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Internal\CheckCompilerFlag.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Internal\CheckFlagCommonConfig.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Internal\CheckSourceCompiles.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Internal\FeatureTesting.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Linker\Windows-MSVC-C.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Linker\Windows-MSVC-CXX.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Linker\Windows-MSVC.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Windows-Determine-CXX.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Windows-Initialize.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Windows-MSVC-C.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Windows-MSVC-CXX.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Windows-MSVC.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Windows.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\WindowsPaths.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\WriteBasicConfigVersionFile.cmake;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\build_win64\CMakeFiles\3.31.6-msvc6\CMakeCCompiler.cmake;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\build_win64\CMakeFiles\3.31.6-msvc6\CMakeCXXCompiler.cmake;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\build_win64\CMakeFiles\3.31.6-msvc6\CMakeRCCompiler.cmake;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\build_win64\CMakeFiles\3.31.6-msvc6\CMakeSystem.cmake;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\tools\cmake_tools.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\build_win64\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Building Custom Rule D:/BaiduNetdiskDownload/mhgd/rc/project/usrzone/source/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SD:/BaiduNetdiskDownload/mhgd/rc/project/usrzone/source -BD:/BaiduNetdiskDownload/mhgd/rc/project/usrzone/source/build_win64 --check-stamp-file D:/BaiduNetdiskDownload/mhgd/rc/project/usrzone/source/build_win64/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeCCompiler.cmake.in;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeCCompilerABI.c;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeCInformation.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeCXXCompiler.cmake.in;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeCXXCompilerABI.cpp;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeCXXInformation.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeCheckCompilerFlagCommonPatterns.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeCommonLanguageInclude.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeCompilerIdDetection.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeDependentOption.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeDetermineCCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeDetermineCXXCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeDetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeDetermineCompilerABI.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeDetermineCompilerId.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeDetermineCompilerSupport.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeDetermineRCCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeDetermineSystem.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeFindBinUtils.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeGenericSystem.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeInitializeConfigs.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeLanguageInformation.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakePackageConfigHelpers.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeParseImplicitIncludeInfo.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeParseImplicitLinkInfo.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeParseLibraryArchitecture.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeRCCompiler.cmake.in;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeRCInformation.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeSystem.cmake.in;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeSystemSpecificInformation.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeSystemSpecificInitialize.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeTestCCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeTestCXXCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeTestCompilerCommon.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeTestRCCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CTest.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CTestUseLaunchers.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CheckCCompilerFlag.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CheckCSourceCompiles.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\ADSP-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\ARMCC-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\ARMClang-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\AppleClang-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\Borland-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\Bruce-C-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\CMakeCommonCompilerMacros.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\Clang-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\Clang-DetermineCompilerInternal.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\Compaq-C-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\Compaq-CXX-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\Cray-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\CrayClang-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\Embarcadero-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\Fujitsu-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\FujitsuClang-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\GHS-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\GNU-C-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\GNU-CXX-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\HP-C-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\HP-CXX-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\IAR-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\IBMCPP-C-DetermineVersionInternal.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\IBMCPP-CXX-DetermineVersionInternal.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\IBMClang-C-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\IBMClang-CXX-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\Intel-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\IntelLLVM-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\LCC-C-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\LCC-CXX-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\MSVC-C.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\MSVC-CXX.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\MSVC-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\MSVC.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\NVHPC-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\NVIDIA-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\OpenWatcom-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\OrangeC-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\PGI-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\PathScale-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\SCO-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\SDCC-C-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\SunPro-C-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\SunPro-CXX-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\TI-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\TIClang-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\Tasking-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\TinyCC-C-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\VisualAge-C-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\VisualAge-CXX-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\Watcom-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\XL-C-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\XL-CXX-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\XLClang-C-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\XLClang-CXX-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\zOS-C-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\zOS-CXX-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CompilerId\VS-10.vcxproj.in;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\GNUInstallDirs.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Internal\CMakeCLinkerInformation.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Internal\CMakeCXXLinkerInformation.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Internal\CMakeCommonLinkerInformation.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Internal\CMakeDetermineLinkerId.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Internal\CheckCompilerFlag.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Internal\CheckFlagCommonConfig.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Internal\CheckSourceCompiles.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Internal\FeatureTesting.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Linker\Windows-MSVC-C.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Linker\Windows-MSVC-CXX.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Linker\Windows-MSVC.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Windows-Determine-CXX.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Windows-Initialize.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Windows-MSVC-C.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Windows-MSVC-CXX.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Windows-MSVC.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Windows.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\WindowsPaths.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\WriteBasicConfigVersionFile.cmake;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\build_win64\CMakeFiles\3.31.6-msvc6\CMakeCCompiler.cmake;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\build_win64\CMakeFiles\3.31.6-msvc6\CMakeCXXCompiler.cmake;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\build_win64\CMakeFiles\3.31.6-msvc6\CMakeRCCompiler.cmake;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\build_win64\CMakeFiles\3.31.6-msvc6\CMakeSystem.cmake;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\tools\cmake_tools.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\build_win64\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Building Custom Rule D:/BaiduNetdiskDownload/mhgd/rc/project/usrzone/source/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SD:/BaiduNetdiskDownload/mhgd/rc/project/usrzone/source -BD:/BaiduNetdiskDownload/mhgd/rc/project/usrzone/source/build_win64 --check-stamp-file D:/BaiduNetdiskDownload/mhgd/rc/project/usrzone/source/build_win64/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeCCompiler.cmake.in;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeCCompilerABI.c;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeCInformation.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeCXXCompiler.cmake.in;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeCXXCompilerABI.cpp;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeCXXInformation.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeCheckCompilerFlagCommonPatterns.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeCommonLanguageInclude.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeCompilerIdDetection.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeDependentOption.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeDetermineCCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeDetermineCXXCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeDetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeDetermineCompilerABI.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeDetermineCompilerId.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeDetermineCompilerSupport.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeDetermineRCCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeDetermineSystem.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeFindBinUtils.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeGenericSystem.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeInitializeConfigs.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeLanguageInformation.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakePackageConfigHelpers.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeParseImplicitIncludeInfo.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeParseImplicitLinkInfo.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeParseLibraryArchitecture.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeRCCompiler.cmake.in;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeRCInformation.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeSystem.cmake.in;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeSystemSpecificInformation.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeSystemSpecificInitialize.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeTestCCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeTestCXXCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeTestCompilerCommon.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeTestRCCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CTest.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CTestUseLaunchers.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CheckCCompilerFlag.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CheckCSourceCompiles.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\ADSP-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\ARMCC-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\ARMClang-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\AppleClang-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\Borland-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\Bruce-C-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\CMakeCommonCompilerMacros.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\Clang-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\Clang-DetermineCompilerInternal.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\Compaq-C-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\Compaq-CXX-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\Cray-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\CrayClang-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\Embarcadero-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\Fujitsu-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\FujitsuClang-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\GHS-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\GNU-C-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\GNU-CXX-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\HP-C-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\HP-CXX-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\IAR-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\IBMCPP-C-DetermineVersionInternal.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\IBMCPP-CXX-DetermineVersionInternal.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\IBMClang-C-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\IBMClang-CXX-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\Intel-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\IntelLLVM-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\LCC-C-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\LCC-CXX-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\MSVC-C.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\MSVC-CXX.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\MSVC-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\MSVC.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\NVHPC-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\NVIDIA-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\OpenWatcom-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\OrangeC-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\PGI-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\PathScale-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\SCO-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\SDCC-C-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\SunPro-C-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\SunPro-CXX-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\TI-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\TIClang-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\Tasking-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\TinyCC-C-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\VisualAge-C-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\VisualAge-CXX-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\Watcom-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\XL-C-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\XL-CXX-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\XLClang-C-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\XLClang-CXX-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\zOS-C-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\zOS-CXX-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CompilerId\VS-10.vcxproj.in;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\GNUInstallDirs.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Internal\CMakeCLinkerInformation.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Internal\CMakeCXXLinkerInformation.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Internal\CMakeCommonLinkerInformation.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Internal\CMakeDetermineLinkerId.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Internal\CheckCompilerFlag.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Internal\CheckFlagCommonConfig.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Internal\CheckSourceCompiles.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Internal\FeatureTesting.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Linker\Windows-MSVC-C.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Linker\Windows-MSVC-CXX.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Linker\Windows-MSVC.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Windows-Determine-CXX.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Windows-Initialize.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Windows-MSVC-C.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Windows-MSVC-CXX.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Windows-MSVC.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Windows.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\WindowsPaths.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\WriteBasicConfigVersionFile.cmake;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\build_win64\CMakeFiles\3.31.6-msvc6\CMakeCCompiler.cmake;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\build_win64\CMakeFiles\3.31.6-msvc6\CMakeCXXCompiler.cmake;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\build_win64\CMakeFiles\3.31.6-msvc6\CMakeRCCompiler.cmake;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\build_win64\CMakeFiles\3.31.6-msvc6\CMakeSystem.cmake;D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\tools\cmake_tools.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\build_win64\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\pcute_usrzone.cpp" />
    <ClCompile Include="D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src\dvo\inject_data.cpp" />
    <ClCompile Include="D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src\dvo\system_start.cpp" />
    <ClCompile Include="D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src\protocal\pb_sql.cpp" />
    <ClCompile Include="D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src\saux_usrzone.cpp" />
    <ClCompile Include="D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src\serva\insert_record_log.cpp" />
    <ClCompile Include="D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src\serva\servcmd_s2q_query_player.cpp" />
    <ClCompile Include="D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src\serva\servcmd_s2z_kill_role.cpp" />
    <ClCompile Include="D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src\serva\servcmd_s2z_modify_data.cpp" />
    <ClCompile Include="D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src\serva\servcmd_s2z_read.cpp" />
    <ClCompile Include="D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src\serva\servcmd_s2z_read_mirror.cpp" />
    <ClCompile Include="D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src\serva\servcmd_s2z_read_player.cpp" />
    <ClCompile Include="D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src\serva\servcmd_s2z_remove_data.cpp" />
    <ClCompile Include="D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src\serva\servcmd_s2z_write.cpp" />
    <ClCompile Include="D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src\servd\servcmd_s2z_delete_record_d.cpp" />
    <ClCompile Include="D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src\servd\servcmd_s2z_read_record_d.cpp" />
    <ClCompile Include="D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src\servd\servcmd_s2z_remark_record_d.cpp" />
    <ClCompile Include="D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src\servd\servcmd_s2z_save_record_d.cpp" />
    <ClCompile Include="D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src\service_usrzone.cpp" />
    <ClCompile Include="D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src\servs\servcmd_s2z_purge_cache.cpp" />
    <ClCompile Include="D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src\sql\sql_action.cpp" />
    <ClCompile Include="D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src\sql\sql_thread.cpp" />
    <ClCompile Include="D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src\tools\account_vo.cpp" />
    <ClCompile Include="D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src\tools\record_slot.cpp" />
    <ClCompile Include="D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src\tools\task_manager.cpp" />
    <ClCompile Include="D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src\tools\task_thread.cpp" />
    <ClCompile Include="D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src\tools\transaction_read.cpp" />
    <ClCompile Include="D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src\tools\transaction_write.cpp" />
    <ClCompile Include="D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src_usrzone\src\tools\uzone_tool.cpp" />
  </ItemGroup>
  <ItemGroup />
  <ItemGroup>
    <ProjectReference Include="D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\build_win64\ZERO_CHECK.vcxproj">
      <Project>{2EE08440-C90A-3B61-B0E4-92FBEEED62CD}</Project>
      <Name>ZERO_CHECK</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\build_win64\pcute_aux.vcxproj">
      <Project>{2D6759CD-3415-3438-8872-BA58BD74D4AF}</Project>
      <Name>pcute_aux</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>