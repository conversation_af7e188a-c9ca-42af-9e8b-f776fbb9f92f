﻿@echo off
chcp 65001 >nul 2>&1
setlocal enabledelayedexpansion

echo ���� gameserver ��Ŀ...

set CURRENT_DIR=%CD%
set args=%*

cd project\gameserver\source\

if not exist "make_win64.bat" (
    echo ����: make_win64.bat �ļ������ڣ����ڴ���...
    call :create_make_win64
)

call make_win64.bat %args%
set build_result=%errorlevel%

cd %CURRENT_DIR%

if %build_result% neq 0 (
    echo gameserver ����ʧ�ܣ�
    exit /b %build_result%
) else (
    echo gameserver �����ɹ���
)
goto :eof

:create_make_win64
echo if not exist "build_win64" ^( > make_win64.bat
echo 	mkdir build_win64 >> make_win64.bat
echo ^) >> make_win64.bat
echo pushd build_win64 >> make_win64.bat
echo cmake ../ ^^ >> make_win64.bat
echo 		-G "Visual Studio 16 2019" -A x64 ^^ >> make_win64.bat
echo 		-DCMAKE_BUILD_TYPE=Debug^^ >> make_win64.bat
echo 		-DMAKE_INSTALL=ON ^^ >> make_win64.bat
echo 		-DCMAKE_INSTALL_PREFIX=../../cute_root ^^ >> make_win64.bat
echo 		.. >> make_win64.bat
echo. >> make_win64.bat
echo cmake --build ./ --config Debug >> make_win64.bat
echo cmake --install ./ --config Debug >> make_win64.bat
echo popd >> make_win64.bat
echo. >> make_win64.bat
echo echo "install done" >> make_win64.bat
goto :eof

